<template>
  <el-container style="height: 100%">
    <el-aside width="200px">
      <el-menu
        active-text-color="#ffd04b"
        background-color="#545c64"
        text-color="#fff"
        style="height: 100%"
        :default-active="route.path"
        @select="handleSelect"
      >
        <el-menu-item v-for="menu in menus" :key="menu.path" :index="menu.path">
          <el-icon><Setting /></el-icon>
          <span>{{ menu.title }}</span>
        </el-menu-item>
      </el-menu>
    </el-aside>
    <el-main>
      <el-card shadow="always" :body-style="{ padding: '20px' }">
        <router-view></router-view>
      </el-card>
    </el-main>
  </el-container>
</template>

<script setup>
import { Setting } from "@element-plus/icons-vue"
import { useRoute, useRouter } from "vue-router"

const route = useRoute()
const router = useRouter()
const menus = [
  {
    path: "/generate-email",
    name: "GenerateEmail",
    title: "生成新邮箱"
  },
  {
    path: "/cursor",
    name: "<PERSON>ursor",
    title: "Cursor 账号"
  },
  {
    path: "/augment",
    name: "Augment",
    title: "Augment 账号"
  }
]

const handleSelect = path => {
  router.push(path)
}
</script>

<style lang="scss" scoped></style>
