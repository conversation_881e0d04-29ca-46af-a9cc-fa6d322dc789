import { createWebHashHistory } from "vue-router"
import { createRouter } from "vue-router"

const routes = [
  {
    path: "/",
    redirect: "/generate-email"
  },
  {
    path: "/generate-email",
    name: "GenerateEmail",
    component: () => import("@/views/generate-email/generate-email.vue")
  },
  {
    path: "/cursor",
    name: "<PERSON>urs<PERSON>",
    component: () => import("@/views/cursor/cursor-account.vue")
  },
  {
    path: "/augment",
    name: "Augment",
    component: () => import("@/views/augment/augment-account.vue")
  }
]

const router = createRouter({
  routes,
  history: createWebHashHistory(),
  scrollBehavior: () => ({ left: 0, top: 0, behavior: "smooth" })
})

export default router
