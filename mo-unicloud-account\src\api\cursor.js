import http from "@/utils/http"

export const addAccountAPI = params => {
  return http.post("/cursor/addAccountCursor", { params })
}

export const deleteAccountAPI = params => {
  return http.post("/cursor/deleteAccountCursor", { params })
}

export const updateAccountAPI = params => {
  return http.post("/cursor/updateAccountCursor", { params })
}

export const getAccountListAPI = params => {
  return http.post("/cursor/getAccountCursorList", { params })
}
