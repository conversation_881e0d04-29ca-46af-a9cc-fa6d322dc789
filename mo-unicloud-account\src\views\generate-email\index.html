<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>邮箱生成器</title>
    <script src="/generate-email/js/mock-min.js"></script>
    <style>
      body {
        font-family: Arial, sans-serif;
        background: #f5f5f5;
        padding: 50px 20px;
        margin: 0;
      }

      .container {
        background: white;
        padding: 30px;
        max-width: 600px;
        margin: 0 auto;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      h1 {
        text-align: center;
        margin-bottom: 25px;
        font-size: 1.5rem;
      }

      label {
        display: block;
        margin-bottom: 5px;
      }

      input {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 1rem;
        margin-bottom: 15px;
        box-sizing: border-box;
      }

      select {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 1rem;
        margin-bottom: 15px;
        box-sizing: border-box;
        background-color: white;
        cursor: pointer;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        background-image: url("data:image/svg+xml;utf8,<svg fill='gray' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/></svg>");
        background-repeat: no-repeat;
        background-position: right 10px center;
        padding-right: 30px;
      }

      select:hover {
        border-color: #007bff;
      }

      select option {
        padding: 10px;
        font-size: 1rem;
      }

      select option:hover {
        background-color: #f0f8ff;
      }

      /* 美化下拉选择框的容器 */
      .select-container {
        position: relative;
        margin-bottom: 15px;
      }

      .select-container label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #333;
      }

      input:focus {
        border-color: #007bff;
        outline: none;
      }

      select:focus {
        border-color: #007bff;
        outline: none;
      }

      .btn {
        width: 100%;
        background: #007bff;
        color: white;
        border: none;
        padding: 10px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 1rem;
        margin-bottom: 15px;
      }

      .btn:hover {
        background: #0056b3;
      }

      .result {
        background: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-height: 50px;
      }

      .result-text {
        flex: 1;
        font-family: monospace;
        font-size: 1.2rem;
        font-weight: bold;
      }

      .copy-btn {
        background: #28a745;
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        margin-left: 10px;
        display: none;
      }

      .result.has-content .copy-btn {
        display: block;
      }

      .copy-btn:hover {
        background: #218838;
      }

      .copy-btn.copied {
        background: #17a2b8;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>邮箱生成器</h1>

      <div class="select-container">
        <label for="emailSuffix">邮箱后缀:</label>
        <select id="emailSuffix" class="form-control">
          <!-- 选项将通过JavaScript动态生成 -->
        </select>
      </div>

      <div class="select-container">
        <label for="connector">连接符号:</label>
        <select id="connector" class="form-control">
          <option value="_">下划线 (_)</option>
          <option value=".">点 (.)</option>
          <option value="-">横线 (-)</option>
          <option value="">无连接符</option>
        </select>
      </div>

      <button class="btn" onclick="generateEmails()">生成邮箱</button>
      <div class="result" id="result">
        <div class="result-text" id="resultText">点击按钮生成邮箱...</div>
        <button class="copy-btn" id="copyBtn" onclick="copyEmail()">复制</button>
      </div>
    </div>

    <script>
      // 邮箱后缀数组
      const emailDomains = ["momomomowei.icu", "momomomowei.shop"]

      // 初始化时填充下拉选项
      function initializeEmailDomains() {
        const select = document.getElementById("emailSuffix")
        emailDomains.forEach(domain => {
          const option = document.createElement("option")
          option.value = domain
          option.textContent = domain
          select.appendChild(option)
        })
      }

      // 页面加载时初始化
      document.addEventListener("DOMContentLoaded", initializeEmailDomains)

      function generateEmails() {
        const suffix = document.getElementById("emailSuffix").value || emailDomains[0]
        const connector = document.getElementById("connector").value

        const data = Mock.mock({
          email: function () {
            const firstName = Mock.mock("@first").toLowerCase()
            const lastName = Mock.mock("@last").toLowerCase()
            return `${firstName}${connector}${lastName}@${suffix}`
          }
        })

        const resultText = document.getElementById("resultText")
        const result = document.getElementById("result")
        resultText.innerHTML = data.email
        result.classList.add("has-content")

        // 存储邮箱地址供复制使用
        window.currentEmail = data.email
      }

      function copyEmail() {
        if (window.currentEmail) {
          navigator.clipboard
            .writeText(window.currentEmail)
            .then(() => {
              const copyBtn = document.getElementById("copyBtn")
              const originalText = copyBtn.textContent
              copyBtn.textContent = "已复制!"
              copyBtn.classList.add("copied")

              setTimeout(() => {
                copyBtn.textContent = originalText
                copyBtn.classList.remove("copied")
              }, 2000)
            })
            .catch(() => {
              // 如果现代API不可用，使用传统方法
              const textArea = document.createElement("textarea")
              textArea.value = window.currentEmail
              document.body.appendChild(textArea)
              textArea.select()
              document.execCommand("copy")
              document.body.removeChild(textArea)

              const copyBtn = document.getElementById("copyBtn")
              const originalText = copyBtn.textContent
              copyBtn.textContent = "已复制!"
              copyBtn.classList.add("copied")

              setTimeout(() => {
                copyBtn.textContent = originalText
                copyBtn.classList.remove("copied")
              }, 2000)
            })
        }
      }
    </script>
  </body>
</html>
